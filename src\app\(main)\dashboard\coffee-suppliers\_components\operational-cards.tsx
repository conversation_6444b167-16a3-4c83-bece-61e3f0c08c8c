"use client";

import { Check<PERSON>ir<PERSON>, Clock, AlertTriangle, Package } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardAction, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";

// Supply Chain Action Items
const supplyChainActionItems = [
  {
    id: 1,
    title: "Ethiopian Coffee Shipment",
    desc: "2.5 tons premium coffee beans arriving next week",
    due: "Due in 5 days",
    priority: "High",
    priorityColor: "bg-green-100 text-green-700",
    checked: false,
    icon: Package
  },
  {
    id: 2,
    title: "Equipment Maintenance",
    desc: "Quarterly maintenance for espresso machines",
    due: "Due tomorrow",
    priority: "Medium",
    priorityColor: "bg-yellow-100 text-yellow-700",
    checked: true,
    icon: CheckCircle
  },
  {
    id: 3,
    title: "Supplier Contract Renewal",
    desc: "Colombian supplier contract expires this month",
    due: "Due this week",
    priority: "High",
    priorityColor: "bg-red-100 text-red-700",
    checked: false,
    icon: AlertTriangle
  },
  {
    id: 4,
    title: "Inventory Audit",
    desc: "Monthly inventory count and quality check",
    due: "Due next week",
    priority: "Low",
    priorityColor: "bg-blue-100 text-blue-700",
    checked: false,
    icon: Clock
  }
];

// Equipment Delivery Status
const equipmentDeliveries = [
  {
    id: 1,
    item: "La Marzocco Espresso Machine",
    supplier: "Coffee Equipment Mongolia",
    status: "In Transit",
    estimatedArrival: "Jan 25, 2024",
    trackingNumber: "UB2024001",
    statusColor: "bg-blue-100 text-blue-700"
  },
  {
    id: 2,
    item: "Mazzer Coffee Grinder",
    supplier: "Barista Equipment UB",
    status: "Delivered",
    estimatedArrival: "Jan 20, 2024",
    trackingNumber: "UB2024002",
    statusColor: "bg-green-100 text-green-700"
  },
  {
    id: 3,
    item: "Coffee Shop Furniture Set",
    supplier: "UB Furniture Co.",
    status: "Processing",
    estimatedArrival: "Jan 30, 2024",
    trackingNumber: "*********",
    statusColor: "bg-yellow-100 text-yellow-700"
  }
];

export function OperationalCards() {
  return (
    <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
      <Card className="shadow-xs">
        <CardHeader>
          <CardTitle>Supply Chain Action Items</CardTitle>
          <CardDescription>
            Critical tasks and deadlines for coffee supply chain management
          </CardDescription>
          <CardAction>
            <Select defaultValue="all">
              <SelectTrigger>
                <SelectValue placeholder="Filter by priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Items</SelectItem>
                <SelectItem value="high">High Priority</SelectItem>
                <SelectItem value="medium">Medium Priority</SelectItem>
              </SelectContent>
            </Select>
          </CardAction>
        </CardHeader>
        <CardContent>
          <Separator />
          <div className="space-y-4 pt-4">
          {supplyChainActionItems.map((item) => {
            const IconComponent = item.icon;
            return (
              <div key={item.id} className="flex items-start gap-3 p-3 rounded-lg border">
                <div className="flex-shrink-0">
                  <IconComponent className="h-5 w-5 text-muted-foreground" />
                </div>
                <div className="flex-1 space-y-1">
                  <div className="flex items-center gap-2">
                    <h4 className="text-sm font-medium">{item.title}</h4>
                    <Badge variant="outline" className={item.priorityColor}>
                      {item.priority}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{item.desc}</p>
                  <p className="text-xs text-muted-foreground">{item.due}</p>
                </div>
                <Button variant="ghost" size="sm">
                  {item.checked ? "Completed" : "Mark Done"}
                </Button>
              </div>
            );
          })}
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-xs">
        <CardHeader>
          <CardTitle>Equipment Delivery Tracking</CardTitle>
          <CardDescription>
            Current status of coffee shop equipment orders and deliveries
          </CardDescription>
          <CardAction>
            <Select defaultValue="all">
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Deliveries</SelectItem>
                <SelectItem value="shipped">Shipped</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
          </CardAction>
        </CardHeader>
        <CardContent>
          <Separator />
          <div className="space-y-4 pt-4">
          {equipmentDeliveries.map((delivery) => (
            <div key={delivery.id} className="flex items-start gap-3 p-3 rounded-lg border">
              <div className="flex-shrink-0">
                <Package className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="flex-1 space-y-1">
                <div className="flex items-center gap-2">
                  <h4 className="text-sm font-medium">{delivery.item}</h4>
                  <Badge variant="outline" className={delivery.statusColor}>
                    {delivery.status}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">{delivery.supplier}</p>
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span>ETA: {delivery.estimatedArrival}</span>
                  <span>Tracking: {delivery.trackingNumber}</span>
                </div>
              </div>
              <Button variant="ghost" size="sm">
                Track
              </Button>
            </div>
          ))}

          <Separator />
          <div className="pt-4">
            <Button variant="outline" className="w-full">
              View All Deliveries
            </Button>
          </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
