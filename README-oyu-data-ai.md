# Oyu-Data-AI Platform - Market Intelligence Dashboard

A cutting-edge data intelligence platform offering structured, curated, and up-to-date datasets for market insights in Mongolia. Built with Next.js 15, TypeScript, and Shadcn UI.

<img src="https://github.com/arhamkhnz/next-shadcn-admin-dashboard/blob/main/media/dashboard.png?version=4" alt="Oyu-Data-AI Dashboard Screenshot">

**Oyu-Data-AI** solves the problem of time-consuming, expensive, and fragmented market research by providing instant access to ready-made datasets and AI-powered insights for the Mongolian market.

Perfect for entrepreneurs, researchers, corporations, and developers who need reliable market intelligence to make faster, data-driven decisions.

> **Featured Dashboard:** Coffee Shop Market Intelligence for Ulaanbaatar

## 🎯 Project Overview

Oyu-Data-AI is a cutting-edge data intelligence platform that offers structured, curated, and up-to-date datasets for individuals, researchers, startups, and large companies who need reliable market insights to make faster, data-driven decisions.

This platform is designed to simplify access to high-quality datasets relevant to the Mongolian market (initially), allowing users to make informed decisions with minimal effort and time.

### 💡 Problem Solved

In many industries, from retail to real estate, companies and individuals often spend months or even years doing manual market research. For example, when a foreign investor wants to open a coffee shop in Mongolia, they may need to find answers to:

1. What type of coffee do Mongolian consumers prefer?
2. What pricing strategies work locally?
3. Which areas already have high coffee shop density?
4. Where have similar businesses failed and why?
5. Which locations offer affordable rental opportunities?
6. What seasonal trends exist for different coffee products?

Such research is time-consuming, expensive, and often fragmented. **Oyu-Data-AI solves this by providing instant access to ready-made datasets and tools to explore custom data in one intelligent platform.**

## 🚀 Features

### Coffee Shop Market Intelligence Dashboard

- **📊 Market Overview Cards**: Total shops, average prices, daily customers, market growth
- **📈 Interactive Charts**: District analysis, price distribution, competition density, seasonal trends
- **🗺️ Location Insights**: Foot traffic analysis, rental prices, investment opportunities
- **📋 Comprehensive Data Table**: Searchable database of all coffee shops with filtering capabilities
- **🎯 AI-Powered Recommendations**: Investment opportunities and market insights

### Key Platform Features

1. **Smart Data Dashboard**
   - User-friendly web dashboard for searching, viewing, and interacting with datasets
   - Filtering by category (industry, location, date, price range, etc.)
   - Visual insights (charts, heatmaps, tables)

2. **Real-time Market Intelligence**
   - Up-to-date datasets covering multiple verticals
   - F&B, retail, demographics, real estate, transportation analysis
   - Competitive intelligence and pricing strategies

3. **Location-Based Analytics**
   - District-wise market analysis
   - Foot traffic and rental price correlations
   - Investment opportunity scoring

## 🛠️ Tech Stack

- **Framework**: Next.js 15 (App Router), TypeScript, Tailwind CSS v4
- **Components**: Shadcn UI
- **Charts**: Recharts for data visualization
- **Data Handling**: Zod for validation
- **State Management**: Zustand
- **Tables**: TanStack Table
- **Tooling**: ESLint, Prettier, Husky

## 📊 Demo Data

The dashboard includes comprehensive demo data for coffee shops in Ulaanbaatar:

- **12 Coffee Shops** across 6 districts
- **Market Analysis** including pricing, competition, and foot traffic
- **Location Intelligence** with rental prices and investment opportunities
- **Seasonal Trends** and growth patterns
- **Investment Recommendations** based on data analysis

## 🏗️ Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd next-shadcn-admin-dashboard
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

The app will automatically redirect to the Oyu-Data-AI dashboard at `/dashboard/oyu-data-ai`.

## 📈 Business Model

### Subscription Tiers

| Tier | Price | Access |
|------|-------|--------|
| **Starter** | $29/mo | Basic datasets, limited AI assistant queries |
| **Pro** | $99/mo | Full access to most datasets, full AI assistant access |
| **Enterprise** | $499/mo | Full access, priority support, API access, custom research |

### Target Users

- **Entrepreneurs**: Feasibility analysis before launching a business
- **Researchers**: Access to local data for academic or private sector studies
- **Large Corporations**: Market entry, competitive intelligence, pricing strategy
- **Government & NGOs**: Policy planning, social research
- **Developers**: Embed local data into their apps or tools

## 🎯 Use Case Example

A European company wants to bring a bakery chain to Mongolia. They use Oyu-Data-AI to:

- ✅ Instantly find which districts have the highest foot traffic
- ✅ Analyze competition density and market saturation
- ✅ View property rental data trends for the last 3 years
- ✅ Get AI-powered consumer preference insights
- ✅ Export a full market analysis report in seconds

**Result**: Months of research compressed into minutes of intelligent data exploration.

## 🔮 Future Vision

- **Data Marketplace** for third-party contributors
- **AI Predictive Insights** (forecasting sales, demographic shifts)
- **Mobile App** for data-on-the-go
- **API Integration** for developers
- **Partnerships** with local institutions for data validation

## 📝 License

This project is based on the Next.js Admin Template and has been customized for the Oyu-Data-AI platform.

---

**Happy Data-Driven Decision Making!** 🚀
